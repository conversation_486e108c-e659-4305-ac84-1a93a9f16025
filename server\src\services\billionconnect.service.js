const axios = require('axios');
const crypto = require('crypto');
const exchangeRateService = require('./exchangeRate.service');

// F041 Email Configuration - Change email address here or set environment variables
const F041_EMAIL_CONFIG = {
    defaultEmail: process.env.F041_DEFAULT_EMAIL,
    autoSendEnabled: process.env.F041_AUTO_SEND_ENABLED !== 'false'
};

class BillionConnectService {
    constructor() {
        this.baseURL = process.env.BILLIONCONNECT_API_URL || 'https://apiint-flow.billionconnect.com/Flow/saler/2.0/invoke';
        this.channelId = process.env.BILLIONCONNECT_CHANNEL_ID;
        this.appSecret = process.env.BILLIONCONNECT_APP_SECRET;
        
        if (!this.channelId || !this.appSecret) {
            console.warn('BillionConnect API credentials not configured');
        }
    }

    /**
     * Generate MD5 signature for BillionConnect API
     * @param {Object} requestBody - The request body object
     * @returns {string} MD5 hash signature
     */
    generateSignature(requestBody) {
        const bodyString = JSON.stringify(requestBody);
        const signatureString = this.appSecret + bodyString;
        return crypto.createHash('md5').update(signatureString, 'utf8').digest('hex');
    }

    /**
     * Get current time in GMT+8 timezone
     * @returns {string} Formatted time string YYYY-MM-DD HH:mm:ss
     */
    getGMT8Time() {
        const now = new Date();
        // Convert to GMT+8 (Beijing time)
        const gmt8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        return gmt8Time.toISOString().slice(0, 19).replace('T', ' ');
    }

    /**
     * Make API request to BillionConnect
     * @param {Object} requestBody - The request body
     * @returns {Promise<Object>} API response
     */
    async makeRequest(requestBody) {
        try {
            const signature = this.generateSignature(requestBody);
            
            const headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'x-channel-id': this.channelId,
                'x-sign-method': 'md5',
                'x-sign-value': signature
            };

            console.log('BillionConnect API Request:', {
                url: this.baseURL,
                headers: { ...headers, 'x-sign-value': '[HIDDEN]' },
                body: requestBody
            });

            const response = await axios.post(this.baseURL, requestBody, { headers });
            
            console.log('BillionConnect API Response:', {
                status: response.status,
                tradeCode: response.data?.tradeCode,
                tradeMsg: response.data?.tradeMsg
            });

            return this.handleResponse(response);
        } catch (error) {
            console.error('BillionConnect API Error:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw error;
        }
    }

    /**
     * Handle API response and check for errors
     * @param {Object} response - Axios response object
     * @returns {Object} Response data
     */
    handleResponse(response) {
        if (!response.data) {
            throw new Error('Empty response from BillionConnect API');
        }

        const { tradeCode, tradeMsg, tradeData } = response.data;

        console.log('BillionConnect response details:', {
            tradeCode,
            tradeMsg,
            hasTradeData: !!tradeData
        });

        return response.data;
    }

    /**
     * Get available commodities using F002 tradeType
     * @param {string} salesMethod - Sales method (default: "5" for distribution)
     * @param {string} language - Language code (default: "2" for English)
     * @returns {Promise<Array>} Array of available products
     */
    async getCommodities(salesMethod = "5", language = "2") {
        try {
            const requestBody = {
                tradeType: "F002",
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    salesMethod,
                    language
                }
            };

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                console.error('BillionConnect API error:', response.tradeMsg);
                return [];
            }

            const commodities = response.tradeData;

            if (!Array.isArray(commodities)) {
                console.error('Expected array of commodities but got:', typeof commodities);
                console.error('Response tradeData:', commodities);
                return [];
            }

            console.log(`Retrieved ${commodities.length} commodities from BillionConnect`);
            return commodities;
        } catch (error) {
            console.error('Error fetching BillionConnect commodities:', error);
            return [];
        }
    }

    /**
     * Get filtered copies based on available price tiers
     * @param {Array} priceTiers - Array of price tier objects with copies property
     * @returns {Array} Filtered array of copy numbers
     */
    getFilteredCopies(priceTiers) {
        if (!Array.isArray(priceTiers) || priceTiers.length === 0) {
            return [1]; 
        }

        const availableCopies = priceTiers
            .map(tier => parseInt(tier.copies))
            .filter(copies => !isNaN(copies) && copies > 0)
            .sort((a, b) => a - b);

        if (availableCopies.length === 0) {
            return [1]; 
        }

        const maxCopies = Math.max(...availableCopies);
        const preferredCopies = [1, 3, 5, 7, 10, 15, 30];

        if (maxCopies >= 30) {
            return preferredCopies.filter(preferred => availableCopies.includes(preferred));
        } else {
            return availableCopies;
        }
    }

    /**
     * Transform BillionConnect product to our standard format
     * @param {Object} product - BillionConnect product object
     * @param {Array} priceTiers - Optional price tiers for creating variants
     * @returns {Object|Array} Standardized product object or array of product variants
     */
    transformProduct(product, priceTiers = null) {
        try {
            let isUnlimited = false;
            let dataAmountKB = 0;
            let capacity = 0; 

            console.log(`Processing BillionConnect product ${product.skuId} (type: ${product.type}): capacity = ${product.capacity}, highFlowSize = ${product.highFlowSize}`);

            if (product.capacity !== undefined && product.capacity !== null && product.capacity !== '') {
                const parsedCapacity = parseInt(product.capacity);
                if (!isNaN(parsedCapacity)) {
                    capacity = parsedCapacity;
                    if (parsedCapacity === -1) {
                        isUnlimited = true;
                        console.log(`   → Unlimited plan detected (capacity = -1)`);
                    } else if (parsedCapacity > 0) {
                        dataAmountKB = parsedCapacity;
                        console.log(`   → Fixed plan detected (capacity = ${parsedCapacity})`);
                    } else {
                        isUnlimited = true;
                        capacity = -1;
                        console.log(`   → Invalid capacity value (${parsedCapacity}), treating as unlimited`);
                    }
                } else {
                    console.log(`   → Invalid capacity format, falling back to highFlowSize`);
                    const highFlowSizeValue = product.highFlowSize;
                    if (highFlowSizeValue !== undefined && highFlowSizeValue !== null && highFlowSizeValue !== '') {
                        const parsedHighFlowSize = parseInt(highFlowSizeValue);
                        if (!isNaN(parsedHighFlowSize)) {
                            if (parsedHighFlowSize === -1) {
                                isUnlimited = true;
                                capacity = -1;
                                console.log(`   → Unlimited plan detected (highFlowSize = -1)`);
                            } else {
                                dataAmountKB = parsedHighFlowSize;
                                capacity = parsedHighFlowSize;
                                console.log(`   → Fixed plan detected (highFlowSize = ${parsedHighFlowSize} KB)`);
                            }
                        } else {
                            isUnlimited = true;
                            capacity = -1;
                            console.log(`   → Invalid highFlowSize, treating as unlimited`);
                        }
                    } else {
                        isUnlimited = true;
                        capacity = -1;
                        console.log(`   → No valid data fields, treating as unlimited`);
                    }
                }
            } else {
                console.log(`   → No capacity field, falling back to highFlowSize`);
                const highFlowSizeValue = product.highFlowSize;
                if (highFlowSizeValue !== undefined && highFlowSizeValue !== null && highFlowSizeValue !== '') {
                    const parsedHighFlowSize = parseInt(highFlowSizeValue);
                    if (!isNaN(parsedHighFlowSize)) {
                        if (parsedHighFlowSize === -1) {
                            isUnlimited = true;
                            capacity = -1;
                            console.log(`   → Unlimited plan detected (highFlowSize = -1)`);
                        } else {
                            dataAmountKB = parsedHighFlowSize;
                            capacity = parsedHighFlowSize;
                            console.log(`   → Fixed plan detected (highFlowSize = ${parsedHighFlowSize} KB)`);
                        }
                    } else {
                        isUnlimited = true;
                        capacity = -1;
                        console.log(`   → Invalid highFlowSize, treating as unlimited`);
                    }
                } else {
                    isUnlimited = true;
                    capacity = -1;
                    console.log(`   → No highFlowSize provided, treating as unlimited`);
                }
            }

            let dataAmount, dataUnit;
            if (isUnlimited) {
                dataAmount = null; 
                dataUnit = null; 
            } else {
                if (dataAmountKB >= 1048576) { // >= 1GB in KB
                    dataAmount = parseFloat((dataAmountKB / 1048576).toFixed(1));
                    dataUnit = 'GB';
                } else if (dataAmountKB >= 1024) { // >= 1MB in KB
                    dataAmount = Math.round(dataAmountKB / 1024);
                    dataUnit = 'MB';
                } else if (dataAmountKB > 0) {
                    dataAmount = parseFloat((dataAmountKB / 1024).toFixed(1));
                    dataUnit = 'MB';
                } else {
                    dataAmount = null;
                    dataUnit = null;
                    capacity = -1;
                    isUnlimited = true;
                    console.log(`   → Calculated data amount is 0, treating as unlimited`);
                }
            }

            console.log(`   → Final values: isUnlimited=${isUnlimited}, capacity=${capacity}, dataAmount=${dataAmount}, dataUnit=${dataUnit}`);

            const primaryCountry = product.country && product.country[0];
            const countryName = primaryCountry?.name || 'Unknown';
            const countryCode = primaryCountry?.mcc || '';
            
            const primaryOperator = primaryCountry?.operatorInfo && primaryCountry.operatorInfo[0];
            const networkName = primaryOperator?.operator || 'Unknown';
            const networkType = primaryOperator?.network || '4G';

            // Parse base validity days from product
            const baseDays = parseInt(product.days) || 1;

            // Determine if hotspot is supported (1 = Available, 0 = Not Available)
            const hotspotSupported = product.hotspotSupport === "1";

            // Determine if the product is rechargeable (top-up)
            const rechargeableProduct = product.rechargeableProduct === "1";

            // Determine speed based on limitFlowSpeed (database uses 'Restricted' not 'Restrictive')
            const limitFlowSpeed = product.limitFlowSpeed;
            const speed = limitFlowSpeed && limitFlowSpeed !== '0' && limitFlowSpeed !== '' ? 'Restricted' : 'Unrestricted';

            // Create base product object
            const baseProduct = {
                id: product.skuId, 
                externalSkuId: product.skuId, 
                externalProductId: product.skuId, 
                name: product.name,
                description: product.desc || product.name,
                planData: dataAmount,
                planDataUnit: dataUnit,
                capacity: capacity, 
                baseDays: baseDays, 
                networkName: networkName,
                networkType: networkType,
                hotspot: hotspotSupported,
                top_up: rechargeableProduct,
                isUnlimited: isUnlimited,
                speed: speed, 
                activationPolicy: 'Activation upon first usage',
                country: {
                    id: countryCode,
                    name: countryName
                },
                supportedCountries: (product.country || []).map(c => c.mcc),
                providerMetadata: {
                    skuId: product.skuId,
                    type: product.type,
                    highFlowSize: product.highFlowSize,
                    limitFlowSpeed: product.limitFlowSpeed,
                    hotspotSupport: product.hotspotSupport,
                    countries: product.country,
                    originalProduct: product,
                    isUnlimited: isUnlimited,
                    capacity: capacity,
                    baseDays: baseDays
                }
            };

            // If priceTiers are provided, create variants for different copy counts
            if (priceTiers && Array.isArray(priceTiers)) {
                const filteredCopies = this.getFilteredCopies(priceTiers);

                return filteredCopies.map(copies => {
                    const actualDays = baseDays * copies;
                    const variantName = copies === 1 ?
                        baseProduct.name :
                        `${baseProduct.name} (${actualDays} Days)`;
                    const variantId = `${product.skuId}_${copies}`;

                    return {
                        ...baseProduct,
                        id: variantId, 
                        externalProductId: variantId, 
                        externalSkuId: product.skuId, 
                        name: variantName,
                        validityDays: actualDays, // Actual days = base days * copies
                        providerMetadata: {
                            ...baseProduct.providerMetadata,
                            copies: copies,
                            actualDays: actualDays,
                            baseSkuId: product.skuId 
                        }
                    };
                });
            }

            return {
                ...baseProduct,
                validityDays: baseDays
            };
        } catch (error) {
            console.error('Error transforming BillionConnect product:', error);
            return null;
        }
    }

    /**
     * Get price for specific plans using F003 tradeType
     * @param {Array} planRequests - Array of plan request objects with skuId and copies
     * @param {string} salesMethod - Sales method (default: "5" for distribution)
     * @param {string} language - Language code (default: "2" for English)
     * @returns {Promise<Array>} Array of price information
     */
    async getPlanPrices(planRequests, salesMethod = "5", language = "2") {
        try {
            if (!Array.isArray(planRequests) || planRequests.length === 0) {
                throw new Error('planRequests must be a non-empty array');
            }

            const validatedRequests = planRequests.map(request => {
                if (!request.skuId) {
                    throw new Error('Each plan request must have a skuId');
                }
                return {
                    skuId: request.skuId.toString(),
                    copies: request.copies || request.quantity || 1
                };
            });

            const requestBody = {
                tradeType: "F003",
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    salesMethod,
                    language,
                    planList: validatedRequests
                }
            };

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                console.error('BillionConnect API error:', response.tradeMsg);
                return [];
            }

            const priceData = response.tradeData;

            if (!Array.isArray(priceData)) {
                console.error('Expected array of price data but got:', typeof priceData);
                console.error('Response tradeData:', priceData);
                return [];
            }

            console.log(`Retrieved price information for ${priceData.length} plans from BillionConnect`);
            return priceData;
        } catch (error) {
            console.error('Error fetching BillionConnect plan prices:', error);
            return [];
        }
    }

    /**
     * Get price for a single plan
     * @param {string} skuId - The SKU ID of the plan
     * @param {number} quantity - Number of plans (default: 1)
     * @param {string} salesMethod - Sales method (default: "5" for distribution)
     * @param {string} language - Language code (default: "2" for English)
     * @returns {Promise<Object|null>} Price information object or null
     */
    async getSinglePlanPrice(skuId, quantity = 1, salesMethod = "5", language = "2") {
        try {
            const priceData = await this.getPlanPrices([{ skuId, copies: quantity }], salesMethod, language);

            const planPrice = priceData.find(plan => plan.skuId === skuId.toString());

            if (!planPrice) {
                console.log(`No price data found for SKU: ${skuId}`);
                return null;
            }

            return await this.transformPriceData(planPrice, quantity);
        } catch (error) {
            console.error('Error fetching single plan price:', error);
            return null;
        }
    }

    /**
     * Convert CNY to USD using real-time exchange rate
     * @param {number} cnyAmount - Amount in Chinese Yuan
     * @param {boolean} forceRefresh - Force refresh exchange rate from API
     * @returns {Promise<number>} Amount in USD
     */
    async convertCNYToUSD(cnyAmount, forceRefresh = false) {
        return await exchangeRateService.convertCNYToUSD(cnyAmount, forceRefresh);
    }

    /**
     * Transform BillionConnect price data to our standard format
     * @param {Object} planPriceData - BillionConnect plan price data object with skuId and price array
     * @param {number} requestedQuantity - The requested quantity to find price for
     * @returns {Promise<Object>} Standardized price object
     */
    async transformPriceData(planPriceData, requestedQuantity = 1) {
        try {
            if (!planPriceData.price || !Array.isArray(planPriceData.price)) {
                console.error('Invalid price data structure:', planPriceData);
                return null;
            }

            let selectedPrice = null;

            selectedPrice = planPriceData.price.find(p => parseInt(p.copies) === requestedQuantity);

            if (!selectedPrice) {
                const sortedPrices = planPriceData.price
                    .filter(p => parseInt(p.copies) <= requestedQuantity)
                    .sort((a, b) => parseInt(b.copies) - parseInt(a.copies));

                selectedPrice = sortedPrices[0] || planPriceData.price[0];
            }

            if (!selectedPrice) {
                console.error('No price data found for plan:', planPriceData.skuId);
                return null;   
            }

            const settlementPriceCNY = parseFloat(selectedPrice.settlementPrice);
            const retailPriceCNY = parseFloat(selectedPrice.retailPrice);
            const copies = parseInt(selectedPrice.copies) || 1;

            if (isNaN(settlementPriceCNY) || settlementPriceCNY <= 0) {
                console.error(`Invalid settlement price for SKU ${planPriceData.skuId}: ${selectedPrice.settlementPrice}`);
                return null; // Return null instead of zero price
            }

            const currentExchangeRate = await exchangeRateService.getCNYToUSDRate();

            const settlementPriceUSD = await this.convertCNYToUSD(settlementPriceCNY);
            const retailPriceUSD = await this.convertCNYToUSD(isNaN(retailPriceCNY) ? settlementPriceCNY : retailPriceCNY);

            return {
                skuId: planPriceData.skuId,
                externalSkuId: planPriceData.skuId,
                buyingPrice: settlementPriceUSD,
                retailPrice: retailPriceUSD,
                quantity: copies,
                requestedQuantity: requestedQuantity,
                currency: 'USD', 
                totalPrice: settlementPriceUSD * requestedQuantity,
                pricePerUnit: settlementPriceUSD,
                allPriceTiers: await Promise.all(planPriceData.price.map(async p => ({
                    copies: parseInt(p.copies),
                    settlementPrice: await this.convertCNYToUSD(parseFloat(p.settlementPrice)),
                    retailPrice: await this.convertCNYToUSD(parseFloat(p.retailPrice)),
                    originalSettlementPriceCNY: parseFloat(p.settlementPrice),
                    originalRetailPriceCNY: parseFloat(p.retailPrice)
                }))),
                providerMetadata: {
                    originalPriceData: planPriceData,
                    selectedPriceTier: selectedPrice,
                    originalPriceCNY: {
                        settlementPrice: settlementPriceCNY,
                        retailPrice: retailPriceCNY
                    },
                    exchangeRate: currentExchangeRate,
                    exchangeRateTimestamp: new Date().toISOString(),
                    provider: 'billionconnect'
                }
            };
        } catch (error) {
            console.error('Error transforming BillionConnect price data:', error);
            return null;
        }
    }

    /**
     * Get prices for multiple plans and transform them
     * @param {Array} planRequests - Array of plan request objects
     * @returns {Promise<Array>} Array of standardized price objects
     */
    async getTransformedPrices(planRequests) {
        try {
            const priceData = await this.getPlanPrices(planRequests);

            const quantityMap = new Map();
            planRequests.forEach(request => {
                quantityMap.set(request.skuId.toString(), request.copies || 1);
            });

            const transformedPrices = await Promise.all(
                priceData.map(async planPrice => {
                    const requestedQuantity = quantityMap.get(planPrice.skuId) || 1;
                    return await this.transformPriceData(planPrice, requestedQuantity);
                })
            );

            const validPrices = transformedPrices.filter(price => price !== null);

            console.log(`Transformed ${validPrices.length} BillionConnect price records`);
            return validPrices;
        } catch (error) {
            console.error('Error getting transformed BillionConnect prices:', error);
            return [];
        }
    }

    /**
     * Get all products and transform them to our standard format (without variants)
     * @returns {Promise<Array>} Array of standardized products
     */
    async getProducts() {
        try {
            const commodities = await this.getCommodities();

            const filteredCommodities = commodities.filter(product => {
                const productType = product.type;
                const isValidType = productType === '3105' || productType === '3106'|| productType === '110' || productType === '111' || productType === 3105 || productType === 3106 || productType === 110 || productType === 111;

                if (!isValidType) {
                    console.log(`   → Skipping product ${product.skuId} (${product.name}) - type ${productType} not in allowed types [3105, 3106, 110, 111]`);
                }

                return isValidType;
            });

            console.log(`Filtered ${filteredCommodities.length} products from ${commodities.length} total (only type 3105, 3106, 110, 111)`);

            const transformedProducts = filteredCommodities
                .map(product => this.transformProduct(product))
                .filter(product => product !== null);

            console.log(`Transformed ${transformedProducts.length} BillionConnect products`);
            return transformedProducts;
        } catch (error) {
            console.error('Error getting BillionConnect products:', error);
            return [];
        }
    }

    /**
     * Get all products with variants based on available copies from price tiers - Memory optimized
     * @param {Array} skuIds - Optional array of specific SKU IDs to get variants for
     * @returns {Promise<Array>} Array of product variants
     */
    async getProductVariants(skuIds = null) {
        const memoryMonitor = require('../utils/memoryMonitor');

        try {
            if (!memoryMonitor.isSafeToProcess()) {
                console.log('⚠️ Memory pressure detected in getProductVariants...');
                await memoryMonitor.waitForMemory(10000);
            }

            const commodities = await this.getCommodities();

            const filteredCommodities = commodities.filter(product => {
                const productType = product.type;
                const isValidType = productType === '3105' || productType === '3106'|| productType === '110' || productType === '111' || productType === 3105 || productType === 3106 || productType === 110 || productType === 111;

                if (!isValidType) {
                    console.log(`   → Skipping product ${product.skuId} (${product.name}) - type ${productType} not in allowed types [3105, 3106, 110, 111]`);
                }

                return isValidType;
            });

            const targetCommodities = skuIds ?
                filteredCommodities.filter(product => skuIds.includes(product.skuId)) :
                filteredCommodities;

            if (targetCommodities.length === 0) {
                console.log('No products found for the requested criteria');
                return [];
            }

            console.log(`🔄 Processing ${targetCommodities.length} products for variant creation`);

            const batchSize = Math.min(memoryMonitor.getRecommendedBatchSize(), 20); // Smaller batches for variant creation
            const allVariants = [];

            for (let i = 0; i < targetCommodities.length; i += batchSize) {
                const batch = targetCommodities.slice(i, i + batchSize);
                console.log(`🔄 Processing variant batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(targetCommodities.length / batchSize)} (${batch.length} products)`);

                const priceRequests = batch.map(product => ({
                    skuId: product.skuId,
                    copies: 1 
                }));

                const priceData = await this.getPlanPrices(priceRequests);

                const priceTierMap = new Map();
                priceData.forEach(planPrice => {
                    if (planPrice.price && Array.isArray(planPrice.price)) {
                        priceTierMap.set(planPrice.skuId, planPrice.price);
                    }
                });

                for (const product of batch) {
                    const priceTiers = priceTierMap.get(product.skuId);
                    const variants = this.transformProduct(product, priceTiers);

                    if (Array.isArray(variants)) {
                        allVariants.push(...variants);
                    } else if (variants) {
                        allVariants.push(variants);
                    }
                }

                priceTierMap.clear();

                if (memoryMonitor.isMemoryWarning()) {
                    console.log(`🗑️ Forcing GC after variant batch ${Math.floor(i / batchSize) + 1}`);
                    memoryMonitor.forceGarbageCollection();
                }

                if (i + batchSize < targetCommodities.length) {
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            }

            console.log(`✅ Created ${allVariants.length} product variants from ${targetCommodities.length} base products`);
            return allVariants;
        } catch (error) {
            console.error('Error getting BillionConnect product variants:', error);
            return [];
        }
    }

    /**
     * Get products with their current prices (using variants) - Memory optimized version
     * @param {Array} skuIds - Optional array of specific SKU IDs to get prices for
     * @returns {Promise<Array>} Array of product variants with price information
     */
    async getProductsWithPrices(skuIds = null) {
        const memoryMonitor = require('../utils/memoryMonitor');

        try {
            if (!memoryMonitor.isSafeToProcess()) {
                console.log('⚠️ Memory pressure detected, using lightweight approach...');
                await memoryMonitor.waitForMemory(10000);
            }

            const productVariants = await this.getProductVariants(skuIds);

            if (productVariants.length === 0) {
                console.log('No BillionConnect product variants found');
                return [];
            }

            console.log(`🔄 Processing ${productVariants.length} product variants for pricing...`);

            const baseSkuIds = [...new Set(productVariants.map(variant => variant.externalSkuId))];
            console.log(`📊 Found ${baseSkuIds.length} unique base SKUs`);

            const batchSize = memoryMonitor.getRecommendedBatchSize();
            const variantsWithPrices = [];

            for (let i = 0; i < baseSkuIds.length; i += batchSize) {
                const batchSkuIds = baseSkuIds.slice(i, i + batchSize);
                console.log(`🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(baseSkuIds.length / batchSize)} (${batchSkuIds.length} SKUs)`);

                const priceRequests = batchSkuIds.map(skuId => ({
                    skuId: skuId,
                    copies: 1 
                }));

                const priceData = await this.getPlanPrices(priceRequests);

                // Create a map of SKU ID to all price tiers for this batch
                const priceMap = new Map();
                priceData.forEach(planPrice => {
                    if (planPrice.price && Array.isArray(planPrice.price)) {
                        const copiesPriceMap = new Map();
                        planPrice.price.forEach(tier => {
                            copiesPriceMap.set(parseInt(tier.copies), tier);
                        });
                        priceMap.set(planPrice.skuId, copiesPriceMap);
                    }
                });

                const batchVariants = productVariants.filter(variant =>
                    batchSkuIds.includes(variant.externalSkuId)
                );

                for (const variant of batchVariants) {
                    const baseSkuId = variant.externalSkuId;
                    const copies = variant.providerMetadata?.copies || 1;
                    const copiesPriceMap = priceMap.get(baseSkuId);

                    let buyingPrice = 0;
                    let priceInfo = null;

                    if (copiesPriceMap && copiesPriceMap.has(copies)) {
                        const tierPrice = copiesPriceMap.get(copies);
                        const settlementPriceUSD = await this.convertCNYToUSD(parseFloat(tierPrice.settlementPrice));
                        const retailPriceUSD = await this.convertCNYToUSD(parseFloat(tierPrice.retailPrice));

                        buyingPrice = settlementPriceUSD;
                        priceInfo = {
                            skuId: baseSkuId,
                            externalSkuId: baseSkuId,
                            buyingPrice: settlementPriceUSD,
                            retailPrice: retailPriceUSD,
                            quantity: 1, // Always 1 for variants
                            copies: copies,
                            currency: 'USD',
                            originalSettlementPriceCNY: parseFloat(tierPrice.settlementPrice),
                            originalRetailPriceCNY: parseFloat(tierPrice.retailPrice)
                        };
                    } else {
                        console.warn(`⚠️  No price data available for SKU ${baseSkuId} with ${copies} copies`);
                    }

                    variantsWithPrices.push({
                        ...variant,
                        buyingPrice: buyingPrice,
                        priceInfo: priceInfo,
                        hasPriceData: !!priceInfo
                    });
                }

                priceMap.clear();

                // Force garbage collection if memory is getting high
                if (memoryMonitor.isMemoryWarning()) {
                    console.log(`🗑️ Forcing GC after batch ${Math.floor(i / batchSize) + 1}`);
                    memoryMonitor.forceGarbageCollection();
                }

                if (i + batchSize < baseSkuIds.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            console.log(`✅ Retrieved prices for ${variantsWithPrices.length} product variants`);
            return variantsWithPrices;

        } catch (error) {
            console.error('Error getting BillionConnect products with prices:', error);
            return [];
        }
    }

    /**
     * Create an eSIM order
     * @param {Object} orderData - Order details
     * @param {Object} planData - Optional plan data object (database plan or variant)
     * @returns {Promise<Object>} Order creation response
     */
    async createOrder(orderData, planData = null) {
        try {
            if (!orderData.customerReference) {
                throw new Error('Customer reference is required');
            }

            const email ='<EMAIL>';

            let planSkuCopies = '1'; // Default quantity is always 1
            let deviceSkuId = orderData.skuId;

            if (planData) {
                deviceSkuId = planData.externalSkuId || orderData.skuId;

                if (planData.providerMetadata && planData.providerMetadata.copies) {
                    planSkuCopies = planData.providerMetadata.copies.toString();

                    console.log(`Using database variant metadata: baseSkuId=${deviceSkuId}, copies=${planSkuCopies}, actualDays=${planData.providerMetadata.actualDays || planData.validityDays}`);
                } else {
                    planSkuCopies = '1';
                    console.log(`Using regular plan from database: skuId=${deviceSkuId}, copies=1`);
                }
            }

            const requestBody = {
                tradeType: 'F040',
                tradeTime: new Date().toISOString().replace('T', ' ').split('.')[0],
                tradeData: {
                    channelOrderId: orderData.customerReference,
                    email: email,
                    totalAmount: orderData.totalAmount?.toString() || '0',
                    discountAmount: '0',
                    subOrderList: [{
                        channelSubOrderId: `${orderData.customerReference}_sub`,
                        deviceSkuId: deviceSkuId, 
                        deviceSkuPrice: orderData.totalAmount?.toString() || '0',
                        planSkuCopies: planSkuCopies, 
                        number: '1', 
                        discountAmount: '0',
                        rechargeableESIM: '1' // Set to 1 (yes) to indicate eSIM is rechargeable
                    }]
                }
            };

            console.log('Creating BillionConnect order with data:', {
                ...requestBody,
                tradeData: {
                    ...requestBody.tradeData,
                    email: email.replace(/^(.{2})(.*)(@.*)$/, '$1***$3')
                } 
            });

            const response = await this.makeRequest(requestBody);

            // Check if response has the expected structure
            if (!response || response.tradeCode !== '1000') {
                const errorMsg = response?.tradeMsg || 'Unknown error';
                throw new Error(`Order creation failed: ${errorMsg}`);
            }

            // For successful responses (tradeCode === '1000'), create a standardized response
            const orderResult = {
                orderId: response.tradeData?.orderId || orderData.customerReference,
                channelOrderId: orderData.customerReference,
                status: 'PENDING',
                providerResponse: response,
                providerOrderStatus: 'PENDING'
            };

            // Automatically send email using F041 after successful order creation (if enabled)
            if (F041_EMAIL_CONFIG.autoSendEnabled) {
                try {
                    console.log('Automatically sending eSIM email to', F041_EMAIL_CONFIG.defaultEmail, 'for order:', orderResult.orderId);
                    await this.resendEsimEmail(orderResult.orderId);
                    console.log('eSIM email automatically sent to', F041_EMAIL_CONFIG.defaultEmail, 'for order:', orderResult.orderId);
                } catch (emailError) {
                    console.error('Failed to automatically send eSIM email to', F041_EMAIL_CONFIG.defaultEmail, ':', emailError);
                    // Don't fail the order creation due to email error, just log it
                }
            } else {
                console.log('F041 automatic email sending is disabled for order:', orderResult.orderId);
            }

            return orderResult;
        } catch (error) {
            console.error('Error creating BillionConnect order:', error);
            throw error;
        }
    }

    /**
     * Get order status and details
     * @param {string} orderId - External order ID
     * @returns {Promise<Object>} Order details
     */
    async getOrderStatus(orderId) {
        try {
            const requestBody = {
                tradeType: 'F011',
                tradeTime: new Date().toISOString().replace('T', ' ').split('.')[0],
                tradeData: {
                    orderId
                }
            };

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                throw new Error(`Failed to get order status: ${response.tradeMsg}`);
            }

            return {
                orderId: response.tradeData.orderId,
                status: response.tradeData.status,
                providerResponse: response,
                iccid: response.tradeData.iccid,
                qrCodeUrl: response.tradeData.qrCodeContent,
                apn: response.tradeData.apn,
                orderState: response.tradeData.status
            };
        } catch (error) {
            console.error('Error getting BillionConnect order status:', error);
            throw error;
        }
    }

    /**
     * Query eSIM recharge commodities (F052)
     * @param {string} iccid - Card number to be queried
     * @returns {Promise<Array>} Array of available SKU IDs for recharge
     */
    async queryRechargeCommodities(iccid) {
        try {
            const requestBody = {
                tradeType: 'F052',
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    iccid: iccid
                }
            };

            console.log('Querying BillionConnect recharge commodities for ICCID:', iccid);

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                console.error('BillionConnect F052 API error:', response.tradeMsg);
                throw new Error(`Failed to query recharge commodities: ${response.tradeMsg}`);
            }

            // Extract SKU IDs from response
            const skuIds = response.tradeData?.skuId || [];
            console.log(`Found ${skuIds.length} available recharge SKUs for ICCID ${iccid}:`, skuIds);

            return skuIds;

        } catch (error) {
            console.error('Error querying BillionConnect recharge commodities:', error);
            throw error;
        }
    }

    /**
     * Create recharge order (F007)
     * @param {Object} orderData - Order data for recharge
     * @param {string} orderData.channelOrderId - Main order ID of sales channel
     * @param {Array} orderData.iccidArray - Array of ICCID numbers to recharge
     * @param {string} orderData.skuId - Plan commodity ID
     * @param {number} orderData.copies - Number of plan commodity
     * @param {string} [orderData.totalAmount] - Total order amount
     * @param {string} [orderData.comment] - Remarks
     * @returns {Promise<Object>} Order creation response
     */
    async createRechargeOrder(orderData) {
        try {
            const {
                channelOrderId,
                iccidArray,
                skuId,
                copies = 1,
                totalAmount = '0',
                comment = ''
            } = orderData;

            // Validate required fields
            if (!channelOrderId || !iccidArray || !Array.isArray(iccidArray) || iccidArray.length === 0 || !skuId) {
                throw new Error('channelOrderId, iccidArray, and skuId are required for recharge order');
            }

            // Validate ICCID array size (max 500 according to API docs)
            if (iccidArray.length > 500) {
                throw new Error('ICCID array cannot exceed 500 items');
            }

            const requestBody = {
                tradeType: 'F007',
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    channelOrderId: channelOrderId,
                    totalAmount: totalAmount.toString(),
                    discountAmount: '0',
                    orderCreateTime: this.getGMT8Time(),
                    comment: comment,
                    subOrderList: [{
                        channelSubOrderId: `${channelOrderId}_sub`,
                        iccid: iccidArray,
                        skuId: skuId.toString(),
                        copies: copies.toString(),
                        price: '0',
                        discountAmount: '0'
                    }]
                }
            };

            console.log('Creating BillionConnect recharge order:', {
                channelOrderId,
                iccidCount: iccidArray.length,
                skuId,
                copies
            });

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                console.error('BillionConnect F007 API error:', response.tradeMsg);
                throw new Error(`Failed to create recharge order: ${response.tradeMsg}`);
            }

            console.log('BillionConnect recharge order created successfully:', {
                orderId: response.tradeData?.orderId,
                channelOrderId: response.tradeData?.channelOrderId
            });

            return {
                orderId: response.tradeData?.orderId,
                channelOrderId: response.tradeData?.channelOrderId,
                subOrderList: response.tradeData?.subOrderList || [],
                providerResponse: response
            };

        } catch (error) {
            console.error('Error creating BillionConnect recharge order:', error);
            throw error;
        }
    }

    /**
     * Query data plan usage information using F046 API
     * @param {string} orderId - External order ID (BillionConnect order ID)
     * @param {string} channelOrderId - Optional channel order ID
     * @param {string} iccid - ICCID to query usage for
     * @param {string} language - Language: 1-Chinese, 2-English (default: 2)
     * @returns {Promise<Object>} Usage information
     */
    async getUsageInfo(orderId, channelOrderId = null, iccid = null, language = '2') {
        try {
            if (!orderId && !channelOrderId) {
                throw new Error('Either orderId or channelOrderId is required');
            }

            if (!iccid) {
                throw new Error('ICCID is required for usage query');
            }

            const requestBody = {
                tradeType: 'F046',
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    iccid: iccid,
                    language: language
                }
            };

            // Add orderId or channelOrderId based on what's available
            if (orderId) {
                requestBody.tradeData.orderId = orderId;
            }
            if (channelOrderId) {
                requestBody.tradeData.channelOrderId = channelOrderId;
            }

            console.log('Querying BillionConnect usage info:', {
                orderId,
                channelOrderId,
                iccid: iccid.substring(0, 8) + '***' // Mask ICCID in logs
            });

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                console.error('BillionConnect F046 API error:', response.tradeMsg);

                // Return standardized error response
                return {
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'Error fetching usage data',
                    expiryDate: null,
                    lastUpdateTime: new Date().toISOString(),
                    error: response.tradeMsg,
                    usageDetails: null,
                    planStatus: null
                };
            }

            // Process the response data
            const tradeData = response.tradeData;

            // The API documentation is ambiguous about whether tradeData is an object or an array.
            // This code handles both possibilities.
            let subOrderList = null;
            if (Array.isArray(tradeData)) {
                // Case 1: tradeData is an array of sub-orders
                subOrderList = tradeData;
            } else if (tradeData && Array.isArray(tradeData.subOrderList)) {
                // Case 2: tradeData is an object containing subOrderList
                subOrderList = tradeData.subOrderList;
            }

            console.log('BillionConnect F046 response processing:', {
                hasTradeData: !!tradeData,
                subOrderListFound: !!subOrderList,
                subOrderCount: subOrderList ? subOrderList.length : 0
            });

            if (!subOrderList || subOrderList.length === 0) {
                console.log('BillionConnect usage response contains no subOrderList or it is empty.');
                return {
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'No usage data available',
                    expiryDate: null,
                    lastUpdateTime: new Date().toISOString(),
                    usageDetails: null,
                    planStatus: null,
                };
            }

            // Get the first sub-order (assuming single ICCID query)
            const subOrder = subOrderList[0];

            // Parse usage information
            const usageData = this._parseUsageData(subOrder);

            console.log('BillionConnect usage data parsed:', {
                orderId: tradeData ? tradeData.orderId : undefined, // This might be undefined if tradeData is an array
                planStatus: usageData.planStatus,
                dataUsage: usageData.dataUsage,
                dataAllowance: usageData.dataAllowance
            });

            return usageData;

        } catch (error) {
            console.error('Error getting BillionConnect usage info:', error);

            // Return standardized error response
            return {
                dataUsage: null,
                dataAllowance: null,
                status: 'Error fetching usage data',
                expiryDate: null,
                lastUpdateTime: new Date().toISOString(),
                error: error.message,
                usageDetails: null,
                planStatus: null
            };
        }
    }

    /**
     * Resend eSIM email using F041 tradeType
     * @param {string} orderId - Main order ID of flow platform
     * @param {string} email - Email address to send to (optional, defaults to configured email)
     * @returns {Promise<Object>} Response from BillionConnect API
     */
    async resendEsimEmail(orderId, email = F041_EMAIL_CONFIG.defaultEmail) {
        try {
            const requestBody = {
                tradeType: "F041",
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    orderId: orderId,
                    email: email
                }
            };

            console.log('Resending eSIM email via BillionConnect F041:', {
                orderId,
                email: email.replace(/^(.{2})(.*)(@.*)$/, '$1***$3') // Mask email in logs
            });

            const response = await this.makeRequest(requestBody);

            if (response.tradeCode !== '1000') {
                console.error('BillionConnect F041 API error:', response.tradeMsg);
                throw new Error(`Failed to resend eSIM email: ${response.tradeMsg}`);
            }

            console.log('eSIM email resent successfully via BillionConnect F041:', {
                orderId,
                tradeCode: response.tradeCode,
                tradeMsg: response.tradeMsg
            });

            return {
                success: true,
                tradeCode: response.tradeCode,
                tradeMsg: response.tradeMsg,
                providerResponse: response
            };

        } catch (error) {
            console.error('Error resending eSIM email via BillionConnect:', error);
            throw error;
        }
    }

    /**
     * Parse usage data from BillionConnect F046 response
     * @param {Object} subOrder - Sub-order data from F046 response
     * @returns {Object} Parsed usage data
     */
    _parseUsageData(subOrder) {
        try {
            const {
                planStatus,
                planStartTime,
                planEndTime,
                totalDays,
                totalTraffic,
                usageInfoList,
                highFlowSize,
                planType
            } = subOrder;

            // Convert plan status to our standard format
            const statusMap = {
                '0': 'Not Available', // not used
                '1': 'Active',        // in use
                '2': 'Expired',       // used
                '3': 'Cancelled'      // cancelled
            };

            const status = statusMap[planStatus] || 'Unknown';

            // Calculate total usage from usageInfoList
            let totalUsageKB = 0;
            if (usageInfoList && Array.isArray(usageInfoList)) {
                totalUsageKB = usageInfoList.reduce((sum, usage) => {
                    const usageDate = usage.useDate || usage.usedDate; // Handle both 'useDate' and 'usedDate'
                    return sum + (parseInt(usage.usageAmt) || 0);
                }, 0);
            }

            // Convert KB to bytes for consistency with other providers
            const dataUsage = totalUsageKB * 1024;

            // Calculate data allowance
            let dataAllowance = null;
            if (totalTraffic && totalTraffic !== '-1') {
                // totalTraffic is in KB, convert to bytes
                dataAllowance = parseInt(totalTraffic) * 1024;
            } else if (highFlowSize && planType === '1') {
                // For daily plans, calculate total allowance
                const dailyAllowanceKB = parseInt(highFlowSize);
                const days = parseInt(totalDays) || 1;
                dataAllowance = dailyAllowanceKB * days * 1024; // Convert to bytes
            }

            // Parse dates
            let expiryDate = null;
            if (planEndTime) {
                try {
                    expiryDate = new Date(planEndTime).toISOString();
                } catch (dateError) {
                    console.warn('Error parsing planEndTime:', planEndTime);
                }
            }

            // Determine final status based on usage and expiry
            let finalStatus = status;
            const now = new Date();

            if (expiryDate && new Date(expiryDate) < now) {
                finalStatus = 'Expired';
            } else if (dataAllowance && dataUsage >= dataAllowance) {
                finalStatus = 'Data Depleted';
            } else if (status === 'Active') {
                finalStatus = 'Active';
            }

            return {
                dataUsage: dataUsage,
                dataAllowance: dataAllowance,
                status: finalStatus,
                expiryDate: expiryDate,
                lastUpdateTime: new Date().toISOString(),
                planStatus: planStatus,
                usageDetails: {
                    planStartTime: planStartTime,
                    planEndTime: planEndTime,
                    totalDays: totalDays,
                    planType: planType, // 0-total sku, 1-daily sku
                    dailyUsage: usageInfoList || [],
                    highFlowSize: highFlowSize,
                    totalTrafficKB: totalTraffic !== '-1' ? totalTraffic : null
                }
            };

        } catch (error) {
            console.error('Error parsing BillionConnect usage data:', error);
            return {
                dataUsage: null,
                dataAllowance: null,
                status: 'Error parsing usage data',
                expiryDate: null,
                lastUpdateTime: new Date().toISOString(),
                planStatus: null,
                usageDetails: null
            };
        }
    }
}

module.exports = new BillionConnectService();
